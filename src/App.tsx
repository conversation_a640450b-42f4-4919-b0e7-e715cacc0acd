import { BrowserRouter as Router, Routes, Route } from "react-router";
import { ConfigProvider } from "antd";
import SignIn from "@page/auth-pages/SignIn";
import SignUp from "@page/auth-pages/SignUp";
import NotFound from "@page/other-page/NotFound";
import UserProfiles from "@/pages/UserProfiles";
import Videos from "@page/ui-elements/Videos";
import Images from "@page/ui-elements/Images";
import Alerts from "@page/ui-elements/Alerts";
import Badges from "@page/ui-elements/Badges";
import Avatars from "@page/ui-elements/Avatars";
import Buttons from "@page/ui-elements/Buttons";
import LineChart from "@page/Charts/LineChart";
import BarChart from "@page/Charts/BarChart";
import Calendar from "@/pages/Calendar";
import BasicTables from "@page/Tables/BasicTables";
import FormElements from "@page/Forms/FormElements";
import Blank from "@/pages/Blank";
import AppLayout from "./layout/AppLayout";
import { ScrollToTop } from "./components/common/ScrollToTop";
import Home from "@page/Dashboard/Home";
import UserInfo from "@page/User/Info";
import UserInfoDetail from "@page/User/InfoDetail";
import UserProductionStats from "@page/User/UserProductionStats";
import InviteCodeManagement from "@page/User/InviteCodeManagement";
import Sound from "@page/Resource/Sound";
import DigitalHuman from "@page/Resource/DigitalHuman";
import RenderLog from "@/pages/RenderLog";
import { Toaster } from "react-hot-toast";
import { getAntdTheme } from "./config/antd-theme";
import { useTheme } from "./context/ThemeContext";
import { AuthProvider } from "./context/AuthContext";
import ProtectedRoute from "./components/auth/ProtectedRoute";
import { ROUTES } from "@/constants/routes";

// 自定义 toast 样式
const toastOptions = {
  // 默认设置
  className: "",
  duration: 3000,
  success: {
    style: {
      // 浅绿色
      background: "#22c55e",
      color: "#fff",
      fontSize: "14px",
      fontWeight: "500",
    },
  },
  error: {
    style: {
      background: "#ef4444",
      color: "#fff",
      fontSize: "14px",
      fontWeight: "500",
    },
  },
};

export default function App() {
  const { theme } = useTheme();
  const isDark = theme === "dark";

  return (
    <ConfigProvider theme={getAntdTheme(isDark)}>
      <AuthProvider>
        <Router>
          <ScrollToTop />
          <Routes>
            {/* Dashboard Layout - Protected Routes */}
            <Route
              element={
                <ProtectedRoute>
                  <AppLayout />
                </ProtectedRoute>
              }
            >
              <Route index path={ROUTES.HOME} element={<Home />} />
              {/* User page */}
              <Route path={ROUTES.USER.INFO} element={<UserInfo />} />
              <Route path={ROUTES.USER.INFO_DETAIL} element={<UserInfoDetail />} />
              <Route path={ROUTES.USER.PRODUCTION_STATS} element={<UserProductionStats />} />
              <Route path={ROUTES.USER.INVITE_CODE_MANAGEMENT} element={<InviteCodeManagement />} />
              {/* Resources */}
              <Route path={ROUTES.RESOURCE.SOUND} element={<Sound />} />
              <Route path={ROUTES.RESOURCE.DIGITAL_HUMAN} element={<DigitalHuman />} />
              {/* Render Log */}
              <Route path={ROUTES.RENDER_LOG} element={<RenderLog />} />
              {/* Others Page */}
              <Route path="/profile" element={<UserProfiles />} />
              <Route path="/calendar" element={<Calendar />} />

              <Route path="/blank" element={<Blank />} />
              {/* Forms */}
              <Route path="/form-elements" element={<FormElements />} />
              {/* Tables */}
              <Route path="/basic-tables" element={<BasicTables />} />
              {/* Ui Elements */}
              <Route path="/alerts" element={<Alerts />} />
              <Route path="/avatars" element={<Avatars />} />
              <Route path="/badge" element={<Badges />} />
              <Route path="/buttons" element={<Buttons />} />
              <Route path="/images" element={<Images />} />
              <Route path="/videos" element={<Videos />} />
              {/* Charts */}
              <Route path="/line-chart" element={<LineChart />} />
              <Route path="/bar-chart" element={<BarChart />} />
            </Route>

            {/* Auth Layout - Public Routes */}
            <Route path="/signin" element={<SignIn />} />
            <Route path="/signup" element={<SignUp />} />

            {/* Fallback Route */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </Router>
      </AuthProvider>

      {/* Toast Notifications */}
      <Toaster
        position="top-center"
        reverseOrder={false}
        gutter={8}
        containerClassName="toast-container"
        containerStyle={{
          zIndex: 99999, // 设置极高的 z-index 确保在最顶层
        }}
        toastOptions={toastOptions}
      />
    </ConfigProvider>
  );
}
